# 工艺参数转换功能说明

## 功能概述

`CraftParamTransformService.craftParamsTransform()` 方法用于处理工艺参数的转换和更新。该方法根据输入的JSON数据，查询对应的生产任务、执行工步和参数定义，然后更新参数的最小值和最大值。

## 输入格式

输入参数为JSON数组格式，每个数组元素包含以下字段：

```json
[{
    "RLBD_NET_RATE": {
        "MIN": "0.15",
        "MAX": "1.00"
    },
    "taskCode": "25RD70574",
    "paramCode": "PROD_HEAT_TEMP,RLBD_NET_RATE",
    "processCode": "GDL",
    "PROD_HEAT_TEMP": {
        "MIN": "1050",
        "MAX": "1100"
    }
}]
```

### 字段说明

- **taskCode**: 任务编码（必填）
- **paramCode**: 参数编码，多个参数用逗号分隔（必填）
- **processCode**: 工序编码（必填）
- **参数对象**: 如 `RLBD_NET_RATE`、`PROD_HEAT_TEMP` 等，包含 `MIN` 和 `MAX` 值

## 处理流程

1. **解析JSON数组** - 验证输入格式并解析JSON数据
2. **验证必要字段** - 检查 taskCode、paramCode、processCode 是否存在
3. **查询生产任务** - 根据 taskCode 和 processCode 查询 ProductionTask
4. **查询执行工步** - 根据 task 的 stepId 和 taskId 查询 WorkStep
5. **查询参数定义** - 根据 workStep 查询 ParameterDefinition 列表
6. **解析参数编码** - 将 paramCode 按逗号分割获取参数名称列表
7. **更新参数值** - 根据参数名称匹配参数定义，更新 minValue 和 maxValue

## 使用示例

### Java代码调用

```java
@Autowired
private CraftParamTransformService craftParamTransformService;

public void updateCraftParams() {
    String json = """
        [{
            "RLBD_NET_RATE": {
                "MIN": "0.15",
                "MAX": "1.00"
            },
            "taskCode": "25RD70574",
            "paramCode": "PROD_HEAT_TEMP,RLBD_NET_RATE",
            "processCode": "GDL",
            "PROD_HEAT_TEMP": {
                "MIN": "1050",
                "MAX": "1100"
            }
        }]
        """;
    
    try {
        craftParamTransformService.craftParamsTransform(json);
        System.out.println("工艺参数更新成功");
    } catch (RuntimeException e) {
        System.err.println("工艺参数更新失败: " + e.getMessage());
    }
}
```

### REST API调用

如果有对应的Controller，可以通过HTTP请求调用：

```bash
curl -X POST http://localhost:8080/api/craft/params/transform \
  -H "Content-Type: application/json" \
  -d '[{
    "RLBD_NET_RATE": {
      "MIN": "0.15",
      "MAX": "1.00"
    },
    "taskCode": "25RD70574",
    "paramCode": "PROD_HEAT_TEMP,RLBD_NET_RATE",
    "processCode": "GDL",
    "PROD_HEAT_TEMP": {
      "MIN": "1050",
      "MAX": "1100"
    }
  }]'
```

## 异常处理

方法会在以下情况抛出 `RuntimeException`：

1. **输入参数为空** - "输入参数不能为空"
2. **JSON格式错误** - "输入参数必须是JSON数组格式"
3. **参数数组为空** - "参数数组不能为空"
4. **缺少必要字段** - "缺少必要字段: {fieldName}"
5. **未找到生产任务** - "未找到对应的生产任务 - taskCode: {taskCode}, processCode: {processCode}"
6. **未找到执行工步** - "未找到对应的执行工步 - stepId: {stepId}, taskId: {taskId}"
7. **数值格式错误** - "参数 {paramName} 的数值格式错误"

## 注意事项

1. **参数匹配**: 参数定义的匹配基于 `paramCode` 字段（注意实体类中字段名为 `paramCodee`）
2. **数值精度**: MIN 和 MAX 值会转换为 `BigDecimal` 类型，精度为 12 位，小数点后 3 位
3. **事务处理**: 每个参数定义的更新都会立即保存到数据库
4. **日志记录**: 方法会记录详细的处理日志，包括成功和失败的情况

## 数据库影响

该方法会更新以下数据库表：
- `parameter_definition` - 更新参数定义的 `min_value` 和 `max_value` 字段

## 性能考虑

- 每个参数对象都会触发数据库查询和更新操作
- 建议在低并发环境下使用，或考虑批量更新优化
- 大量参数更新时建议分批处理
